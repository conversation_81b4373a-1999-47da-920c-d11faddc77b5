"""
Dynamic Pipeline Module Loader
Loads and registers pipeline modules based on the master configuration
"""
import importlib
import logging
from typing import Dict, List, Any, Tuple
from .pipeline_config import get_master_pipeline_config, get_pipeline_module_count
from .pipeline_registry import PIPELINE_REGISTRY

logger = logging.getLogger(__name__)

class PipelineModuleLoader:
    """Dynamically loads pipeline modules based on master configuration"""
    
    def __init__(self):
        self.master_config = get_master_pipeline_config()
        self.loaded_modules = {}
        self.failed_modules = {}
    
    def load_all_modules(self) -> Tuple[int, int]:
        """
        Load all pipeline modules from the master configuration.
        
        Returns:
            Tuple of (successful_count, failed_count)
        """
        successful = 0
        failed = 0
        
        logger.info(f"🚀 Starting to load {len(self.master_config)} pipeline modules...")
        
        for module_config in self.master_config:
            module_id = module_config["id"]
            script_path = module_config["script_path"]
            
            try:
                # Import the module
                module = importlib.import_module(script_path.rsplit('.', 1)[0])
                
                # Store reference
                self.loaded_modules[module_id] = {
                    "module": module,
                    "config": module_config,
                    "status": "loaded"
                }
                
                logger.info(f"✅ Loaded module: {module_id} ({module_config['name']})")
                successful += 1
                
            except ImportError as e:
                self.failed_modules[module_id] = {
                    "config": module_config,
                    "error": str(e),
                    "status": "failed"
                }
                logger.error(f"❌ Failed to load module: {module_id} - {e}")
                failed += 1
            except Exception as e:
                self.failed_modules[module_id] = {
                    "config": module_config,
                    "error": str(e),
                    "status": "error"
                }
                logger.error(f"💥 Unexpected error loading module: {module_id} - {e}")
                failed += 1
        
        return successful, failed
    
    def get_load_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the module loading process.
        
        Returns:
            Dictionary containing load statistics and details
        """
        total_expected = len(self.master_config)
        total_loaded = len(self.loaded_modules)
        total_failed = len(self.failed_modules)
        
        # Get registered pipeline steps for comparison
        registered_steps = PIPELINE_REGISTRY
        
        return {
            "total_expected": total_expected,
            "total_loaded": total_loaded,
            "total_failed": total_failed,
            "success_rate": (total_loaded / total_expected * 100) if total_expected > 0 else 0,
            "loaded_modules": list(self.loaded_modules.keys()),
            "failed_modules": list(self.failed_modules.keys()),
            "registered_steps_count": len(registered_steps),
            "registered_step_ids": list(registered_steps.keys()),
            "status": "complete" if total_failed == 0 else "partial" if total_loaded > 0 else "failed"
        }
    
    def validate_module_registration(self) -> Dict[str, Any]:
        """
        Validate that loaded modules are properly registered in the PIPELINE_REGISTRY.

        Returns:
            Validation results
        """
        from .pipeline_registry import PIPELINE_REGISTRY, get_pipeline_function

        validation_results = {
            "total_expected": len(self.master_config),
            "total_registered": len(PIPELINE_REGISTRY),
            "missing_registrations": [],
            "extra_registrations": [],
            "valid_registrations": [],
            "is_valid": False
        }

        # Check each expected module against PIPELINE_REGISTRY
        for module_config in self.master_config:
            module_id = module_config["id"]

            # Check if module is in PIPELINE_REGISTRY
            pipeline_func = get_pipeline_function(module_id)
            if pipeline_func:
                validation_results["valid_registrations"].append({
                    "module_id": module_id,
                    "function_name": pipeline_func.__name__,
                    "module": pipeline_func.__module__
                })
            else:
                validation_results["missing_registrations"].append(module_id)
        
        # Check for extra registrations in PIPELINE_REGISTRY
        expected_module_ids = {config["id"] for config in self.master_config}
        for registry_id in PIPELINE_REGISTRY.keys():
            if registry_id not in expected_module_ids:
                validation_results["extra_registrations"].append({
                    "registry_id": registry_id,
                    "function": PIPELINE_REGISTRY[registry_id].__name__
                })

        # Determine if validation passed
        validation_results["is_valid"] = (
            len(validation_results["missing_registrations"]) == 0 and
            len(validation_results["valid_registrations"]) >= len(self.master_config)
        )
        
        return validation_results

# Global loader instance
pipeline_loader = PipelineModuleLoader()

def load_pipeline_modules() -> Dict[str, Any]:
    """
    Load all pipeline modules and return comprehensive status.
    
    Returns:
        Dictionary containing load results and validation
    """
    # Load modules
    successful, failed = pipeline_loader.load_all_modules()
    
    # Get summary
    load_summary = pipeline_loader.get_load_summary()
    
    # Validate registrations
    validation_results = pipeline_loader.validate_module_registration()
    
    # Combine results
    return {
        "load_summary": load_summary,
        "validation": validation_results,
        "successful_loads": successful,
        "failed_loads": failed,
        "total_expected": get_pipeline_module_count()
    }

__all__ = ['PipelineModuleLoader', 'pipeline_loader', 'load_pipeline_modules']
