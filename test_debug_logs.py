#!/usr/bin/env python3
"""
Debug script to check why logs are not appearing
"""

import sys
import os
import logging
import time

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_debug():
    """Debug why logs are not appearing"""
    
    # Setup logging to match backend configuration
    logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(name)s - %(message)s")
    
    # Test different loggers
    doc_logger = logging.getLogger('back_end.document_processor')
    root_logger = logging.getLogger()
    test_logger = logging.getLogger('test_module')
    
    print("🔍 Debug: Testing different loggers...")
    print("=" * 60)
    
    # Test root logger
    print("1. Testing root logger...")
    root_logger.info("ROOT LOGGER TEST: This should appear in both terminals")
    time.sleep(2)
    
    # Test document processor logger
    print("2. Testing document processor logger...")
    doc_logger.info("DOC PROCESSOR TEST: This should appear in both terminals")
    time.sleep(2)
    
    # Test custom logger
    print("3. Testing custom logger...")
    test_logger.info("CUSTOM LOGGER TEST: This should appear in both terminals")
    time.sleep(2)
    
    # Test print statements
    print("4. Testing print statements...")
    print("PRINT TEST: This should appear in both terminals")
    time.sleep(2)
    
    print("\n" + "=" * 60)
    print("✅ Debug test completed!")
    print("\n📋 Check which logs appear in:")
    print("   - IDE Terminal (this console)")
    print("   - Live Terminal (web UI)")
    print("\n🔍 This will help identify the issue!")

if __name__ == "__main__":
    test_debug()
