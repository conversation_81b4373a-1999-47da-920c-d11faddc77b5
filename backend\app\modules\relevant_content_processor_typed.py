"""
Type-hinted Relevant Content Processor Module
Provides strict typing and contracts for relevant content processing functionality.
"""
import os
import sys
import logging
import importlib.util
from typing import Dict, Any, Optional
from pathlib import Path

from ..contracts.api_contracts import RelevantContentParameters, PipelineStepResult
from ..core.terminal_logger import get_terminal_logger

# Get the root directory for imports
root_dir = Path(__file__).parent.parent.parent.parent

async def execute(
    params: RelevantContentParameters,
    logger,
    run_id: str,
    business_flows_path: str,
    output_dir: str,
    document_structure_path: Optional[str] = None,
    **kwargs
) -> PipelineStepResult:
    """
    Process relevant content using the user's RelevantContentProcessor class.

    Args:
        business_flows_path: Path to business flows directory or file
        output_dir: Dynamic output directory for this session
        logger: Terminal logger instance for centralized logging
        run_id: Pipeline run identifier
        document_structure_path: Optional path to document structure file
        max_workers: Maximum number of worker threads (1-10)
        count_token: Whether to count tokens and calculate costs
        enable_caching: Whether to enable caching for relevant content
        **kwargs: Additional parameters
        
    Returns:
        PipelineStepResult: Structured result with status, message, and output files
        
    Raises:
        FileNotFoundError: If required input files are not found
        ImportError: If RelevantContentProcessor class cannot be imported
        ValueError: If parameters are invalid
    """
    step_id = kwargs.get("step_id", "relevant_content_processor")

    try:
        # Parameters are already validated as Pydantic model
        
        logger.info("🚀 Starting Relevant Content Processing...")

        # Log configuration
        config_info = f"""📋 Relevant Content Configuration:
   • Business flows path: {business_flows_path}
   • Output directory: {output_dir}
   • Max workers: {params.max_workers}
   • Count tokens: {params.count_token}
   • Enable caching: {params.enable_caching}"""
        logger.info(config_info)

        # Setup paths using provided output_dir
        relevant_content_dir = os.path.join(output_dir, "relevant_content_processor")
        os.makedirs(relevant_content_dir, exist_ok=True)

        logger.info(f"📁 Created relevant content directory: {relevant_content_dir}")
        
        # Find required input files
        # 1. Document structure (merged JSON)
        merged_json = os.path.join(output_dir, "document_processor", "merged_output.json")
        if not os.path.exists(merged_json):
            # Try alternative paths
            alt_paths = [
                document_structure_path,
                os.path.join(output_dir, "merged_output.json"),
                os.path.join(output_dir, "document_processor", "merged_output.json")
            ]
            
            for alt_path in alt_paths:
                if alt_path and os.path.exists(alt_path):
                    merged_json = alt_path
                    break
            else:
                logger.error(f"❌ Document structure file not found")
                raise FileNotFoundError("Document structure file not found")

        logger.info(f"📄 Using document structure: {merged_json}")
        
        # 2. Business flows directory
        business_flows_dir = business_flows_path
        if not os.path.isdir(business_flows_dir):
            business_flows_dir = os.path.join(output_dir, "business_flows")
        
        if not os.path.exists(business_flows_dir):
            logger.error(f"❌ Business flows directory not found: {business_flows_dir}")
            raise FileNotFoundError(f"Business flows directory not found: {business_flows_dir}")

        logger.info(f"📁 Using business flows directory: {business_flows_dir}")
        
        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))
        
        # Import and use the user's RelevantContentProcessor class
        logger.info("📦 Importing RelevantContentProcessor class...")

        try:
            # Try different import approaches
            try:
                from back_end.relevant_content_processor import RelevantContentProcessor
                logger.info("✅ Successfully imported RelevantContentProcessor (direct import)")
            except ImportError:
                # Fallback to dynamic import
                logger.info("   Trying dynamic import...")
                spec = importlib.util.spec_from_file_location(
                    "relevant_content_processor",
                    os.path.join(root_dir, "back_end", "relevant_content_processor.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                RelevantContentProcessor = module.RelevantContentProcessor
                logger.info("✅ Successfully imported RelevantContentProcessor (dynamic import)")

        except Exception as e:
            logger.error(f"❌ Failed to import RelevantContentProcessor: {e}")
            return PipelineStepResult(
                status="error",
                message=f"Failed to import RelevantContentProcessor: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Setup configuration file path
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")
        
        # Initialize the user's RelevantContentProcessor
        logger.info("🔧 Initializing RelevantContentProcessor...")

        try:
            processor = RelevantContentProcessor(
                business_flows_dir=business_flows_dir,
                document_structure_path=merged_json,
                output_dir=relevant_content_dir,
                config_file=config_file,
                max_workers=params.max_workers,
                count_token=params.count_token,
                enable_caching=params.enable_caching
            )
            logger.info("✅ RelevantContentProcessor initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize RelevantContentProcessor: {e}")
            return PipelineStepResult(
                status="error",
                message=f"Failed to initialize RelevantContentProcessor: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Execute relevant content processing
        logger.info("🚀 Starting relevant content processing...")
        logger.info(f"   Using {params.max_workers} worker threads...")
        
        try:
            # Call the execute method
            result = processor.execute()
            
            if result and result.get("status") == "success":
                logger.info("✅ Relevant content processing completed successfully")

                # Log output files
                output_files = []
                if os.path.exists(relevant_content_dir):
                    output_files = [os.path.join(relevant_content_dir, f) for f in os.listdir(relevant_content_dir)]
                    if output_files:
                        files_info = "\n".join([f"   • {os.path.basename(f)}" for f in output_files])
                        logger.info(f"📄 Output Files:\n{files_info}")
                
                return PipelineStepResult(
                    status="success",
                    message="Relevant content processing completed successfully",
                    step_id=step_id,
                    output_files=output_files,
                    metadata={
                        "max_workers": params.max_workers,
                        "count_token": params.count_token,
                        "enable_caching": params.enable_caching
                    }
                )
            else:
                error_msg = result.get("message", "Unknown error") if result else "No result returned"
                logger.error(f"❌ Relevant content processing failed: {error_msg}")
                return PipelineStepResult(
                    status="error",
                    message=f"Relevant content processing failed: {error_msg}",
                    step_id=step_id,
                    error_details=str(result) if result else "No result returned"
                )
                
        except Exception as e:
            logger.error(f"❌ Exception during relevant content processing: {e}")
            return PipelineStepResult(
                status="error",
                message=f"Exception during relevant content processing: {e}",
                step_id=step_id,
                error_details=str(e)
            )
            
    except Exception as e:
        logger.error(f"❌ Unexpected error in relevant content processing: {e}")
        return PipelineStepResult(
            status="error",
            message=f"Unexpected error: {e}",
            step_id=step_id,
            error_details=str(e)
        )
