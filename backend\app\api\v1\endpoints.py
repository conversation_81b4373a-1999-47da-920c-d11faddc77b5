"""
API endpoints for the pipeline dashboard
"""
import os
import json
import logging
import aiofiles
from datetime import datetime
from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, Query, Depends, UploadFile, File
from fastapi.responses import FileResponse
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)
try:
    from ...core.pipeline_registry import PIPELINE_REGISTRY as pipeline_registry
except ImportError:
    try:
        from app.core.pipeline_registry import PIPELINE_REGISTRY as pipeline_registry
    except ImportError:
        # Last resort - empty registry
        pipeline_registry = {}

from ...models.contracts import (
    ExecuteStepRequest, ExecuteStepResponse, PipelineGraphResponse,
    WorkspaceFilesResponse, WebSocketMessage
)
from ...contracts.api_contracts import (
    PipelineStepResult
)
from .document_endpoints import router as document_router
from .unified_execution import router as unified_router
from .websocket import router as websocket_router
from ...services.discovery import discovery_service
from ...services.executor import executor
from ...services.session import session_manager
from ...core.websocket_manager import connection_manager

router = APIRouter()

# Include endpoints
router.include_router(document_router, prefix="/document", tags=["document_processing"])
router.include_router(unified_router, tags=["unified_execution"])
router.include_router(websocket_router, prefix="/ws", tags=["websocket"])

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a PDF document for processing"""
    try:
        # Validate file type
        if not file.filename.endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Only PDF files are allowed")

        # Create uploads directory
        uploads_dir = "uploads"
        os.makedirs(uploads_dir, exist_ok=True)

        # Save uploaded file
        file_path = os.path.join(uploads_dir, file.filename)

        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        return {
            "success": True,
            "filename": file.filename,
            "file_path": file_path,
            "file_size": len(content),
            "message": f"File '{file.filename}' uploaded successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")

@router.get("/uploads")
async def list_uploaded_files():
    """List all uploaded files"""
    try:
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            return {"files": []}

        files = []
        for filename in os.listdir(uploads_dir):
            if filename.endswith('.pdf'):
                file_path = os.path.join(uploads_dir, filename)
                file_stat = os.stat(file_path)
                files.append({
                    "filename": filename,
                    "file_path": file_path,
                    "file_size": file_stat.st_size,
                    "upload_time": file_stat.st_mtime
                })

        return {"files": files}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list files: {str(e)}")

@router.post("/pipeline/steps/{step_id}/execute")
async def execute_pipeline_step(step_id: str, parameters: Optional[Dict[str, Any]] = None):
    """Execute a specific pipeline step"""
    try:
        # Get the step function from registry
        if step_id not in pipeline_registry:
            raise HTTPException(status_code=404, detail=f"Pipeline step '{step_id}' not found")

        step_info = pipeline_registry[step_id]
        step_function = step_info["function"]

        # Prepare execution parameters
        execution_params = parameters or {}

        # Add context for execution
        execution_context = {
            "session_id": "default",
            "output_dir": "output",
            "step_id": step_id
        }
        execution_params["_context"] = execution_context

        # Execute the step
        result = step_function(**execution_params)

        return {
            "success": True,
            "step_id": step_id,
            "result": result,
            "message": f"Pipeline step '{step_id}' executed successfully"
        }

    except Exception as e:
        return {
            "success": False,
            "step_id": step_id,
            "error": str(e),
            "message": f"Failed to execute pipeline step '{step_id}': {str(e)}"
        }

@router.get("/pipeline/graph", response_model=PipelineGraphResponse)
async def get_pipeline_graph():
    """Get the complete pipeline DAG structure"""
    try:
        dag_data = discovery_service.discover_pipeline_dag()
        return PipelineGraphResponse(**dag_data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to discover pipeline: {str(e)}")

@router.post("/execute/{step_id}", response_model=ExecuteStepResponse)
async def execute_step(step_id: str, request: ExecuteStepRequest):
    """Execute a specific pipeline step"""
    try:
        result = await executor.execute_step(
            step_id=step_id,
            parameters=request.parameters,
            session_id=request.session_id
        )
        return ExecuteStepResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to execute step: {str(e)}")

@router.post("/stop/{step_id}")
async def stop_step_execution(step_id: str, session_id: str = "default"):
    """Stop execution of a running step"""
    try:
        success = await executor.stop_execution(step_id, session_id)
        return {"success": success, "step_id": step_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop execution: {str(e)}")

@router.get("/files")
async def get_file_content(
    path: str = Query(..., description="File path relative to workspace"),
    session_id: str = Query("default", description="Session ID")
):
    """Get content of a file from the workspace"""
    try:
        workspace_dir = session_manager.get_session_workspace(session_id)
        file_path = os.path.join(workspace_dir, path)
        
        # Security check: ensure file is within workspace
        if not os.path.abspath(file_path).startswith(os.path.abspath(workspace_dir)):
            raise HTTPException(status_code=403, detail="Access denied: file outside workspace")
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="File not found")
        
        if not os.path.isfile(file_path):
            raise HTTPException(status_code=400, detail="Path is not a file")
        
        # Determine content type and read file
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext in ['.json']:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                try:
                    json_content = json.loads(content)
                    return {"type": "json", "content": json_content}
                except json.JSONDecodeError:
                    return {"type": "text", "content": content}
        
        elif file_ext in ['.csv', '.txt', '.md', '.py', '.js', '.html', '.css']:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return {"type": "text", "content": content, "extension": file_ext}
        
        elif file_ext in ['.png', '.jpg', '.jpeg', '.gif', '.svg']:
            return FileResponse(file_path)
        
        else:
            # Binary file or unknown type
            return FileResponse(file_path)
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read file: {str(e)}")

@router.get("/workspace/files", response_model=WorkspaceFilesResponse)
async def list_workspace_files(
    session_id: str = Query("default", description="Session ID"),
    subdirectory: str = Query("", description="Subdirectory to list")
):
    """List files in the session workspace"""
    try:
        files_data = session_manager.list_workspace_files(session_id, subdirectory)
        return WorkspaceFilesResponse(**files_data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list files: {str(e)}")

@router.get("/step/{step_id}")
async def get_step_details(step_id: str):
    """Get detailed information about a specific step"""
    try:
        step_details = discovery_service.get_step_details(step_id)
        if not step_details:
            raise HTTPException(status_code=404, detail="Step not found")
        return step_details
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get step details: {str(e)}")

@router.post("/session/create")
async def create_session():
    """Create a new session and return session_id"""
    import uuid
    session_id = f"session_{uuid.uuid4().hex[:8]}"
    session_info = session_manager.create_session(session_id)
    return {
        "session_id": session_id,
        "session_info": session_info,
        "status_endpoint": f"/api/v1/session/{session_id}/status",
        "websocket_endpoint": f"ws://localhost:8000/api/v1/ws/{session_id}"
    }

@router.get("/session/{session_id}/status")
async def get_session_status(session_id: str):
    """Get current status of all steps in a session"""
    try:
        # Tạo session nếu chưa tồn tại
        if not session_manager.get_session(session_id):
            session_manager.create_session(session_id)

        statuses = session_manager.get_all_step_statuses(session_id)
        return {
            "session_id": session_id,
            "step_statuses": statuses,
            "session_exists": True,
            "total_steps": len(statuses)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get session status: {str(e)}")

@router.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time communication"""
    await websocket.accept()
    await session_manager.add_websocket_connection(session_id, websocket)
    
    try:
        # Send initial connection message
        await websocket.send_text(json.dumps({
            "type": "connection",
            "data": {"message": "Connected to session", "session_id": session_id}
        }))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                elif message.get("type") == "terminal_input":
                    # Handle terminal input if needed
                    pass
                    
            except WebSocketDisconnect:
                break
            except Exception as e:
                print(f"WebSocket error: {e}")
                break
                
    finally:
        await session_manager.remove_websocket_connection(session_id, websocket)

@router.websocket("/ws/logs/{run_id}")
async def websocket_logs_endpoint(websocket: WebSocket, run_id: str, client_id: Optional[str] = Query(None)):
    """
    Production-ready WebSocket endpoint for real-time logging.

    Args:
        websocket: The WebSocket connection
        run_id: The pipeline run ID to subscribe to
        client_id: Optional client identifier
    """
    actual_client_id = None
    try:
        # Connect to the connection manager
        actual_client_id = await connection_manager.connect(websocket, run_id, client_id)

        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for incoming messages (for future two-way communication)
                data = await websocket.receive_text()
                message = json.loads(data)

                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }))
                elif message.get("type") == "subscribe":
                    # Handle subscription changes (future feature)
                    pass

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                # Invalid JSON received
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.now().isoformat()
                }))
            except Exception as e:
                logger.error(f"WebSocket error for client {actual_client_id}: {e}")
                break

    except Exception as e:
        logger.error(f"Failed to establish WebSocket connection: {e}")
    finally:
        # Clean up connection
        await connection_manager.disconnect(websocket)

@router.get("/files/{file_id}")
async def serve_file(file_id: str):
    """Serve uploaded PDF files for viewing"""
    try:
        # Look for the file in uploads directory
        uploads_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "uploads")

        # Find file by ID (files are stored with the file_id in the name)
        for filename in os.listdir(uploads_dir):
            if file_id in filename and filename.endswith('.pdf'):
                file_path = os.path.join(uploads_dir, filename)
                if os.path.exists(file_path):
                    return FileResponse(
                        path=file_path,
                        media_type='application/pdf',
                        filename=filename.split('_')[-1]  # Return original filename
                    )

        raise HTTPException(status_code=404, detail="File not found")

    except Exception as e:
        logger.error(f"Error serving file {file_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error serving file: {str(e)}")
