"""
Type-safe document processor module with strict contracts.
This module processes PDF documents and extracts structured content.
"""
import os
import sys
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

# Add back_end to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), "../../../"))

from back_end.document_processor import DocumentProcessor
from ..core.terminal_logger import get_terminal_logger
from ..models.contracts import ProcessingParameters, PipelineStepResult, NodeStatus, TokenUsage

async def execute(
    params: ProcessingParameters,
    logger,
    run_id: str,
    input_path: str,
    output_dir: str,
    **kwargs
) -> PipelineStepResult:
    """
    Execute document processing with strict type contracts.

    Args:
        logger: Terminal logger instance for structured logging
        run_id: Unique identifier for this execution run
        file_path: Path to the input PDF file
        output_dir: Directory where outputs will be saved
        parameters: Processing parameters with validation
        
    Returns:
        PipelineStepResult: Structured result with status, files, and metadata
        
    Raises:
        FileNotFoundError: If input file doesn't exist
        ValueError: If parameters are invalid
        RuntimeError: If processing fails
    """
    from datetime import datetime

    start_time = datetime.now().isoformat()
    step_id = "document_processor"
    file_path = input_path  # Maintain compatibility with existing code
    parameters = params  # Use the Pydantic model

    try:
        # Validate inputs
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Input file not found: {file_path}")
        
        if not file_path.lower().endswith('.pdf'):
            raise ValueError(f"Expected PDF file, got: {file_path}")
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"Starting document processing for: {os.path.basename(file_path)}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Parameters: {parameters.model_dump()}")
        
        # Initialize DocumentProcessor with type-safe parameters
        processor = DocumentProcessor(
            pdf_path=file_path,
            output_dir=output_dir,
            merged_json=os.path.join(output_dir, "merged_output.json"),
            analyzed_json=os.path.join(output_dir, "description_output.json"),
            use_auto_toc=parameters.use_auto_toc,
            count_token=parameters.count_token,
            enable_caching=parameters.enable_caching
        )
        
        # Execute processing steps
        logger.info("Step 1/3: Processing PDF content...")
        processor.process_pdf()

        logger.info("Step 2/3: Creating merged JSON output...")
        processor.merge_to_json()

        if not parameters.skip_llm:
            logger.info("Step 3/3: Analyzing content with LLM...")
            processor.analyze_json()
        else:
            logger.info("Step 3/3: Skipping LLM analysis (skip_llm=True)")
        
        # Collect output files
        output_files: List[str] = []
        merged_path = os.path.join(output_dir, "merged_output.json")
        if os.path.exists(merged_path):
            output_files.append(merged_path)
        
        analyzed_path = os.path.join(output_dir, "description_output.json")
        if os.path.exists(analyzed_path):
            output_files.append(analyzed_path)
        
        # Get token usage if available
        token_usage: Optional[TokenUsage] = None
        if parameters.count_token and hasattr(processor, 'get_token_summary'):
            try:
                token_summary = processor.get_token_summary()
                if token_summary:
                    token_usage = TokenUsage(
                        input_tokens=token_summary.get('total_input_tokens', 0),
                        output_tokens=token_summary.get('total_output_tokens', 0),
                        total_tokens=token_summary.get('total_tokens', 0),
                        cost_usd=token_summary.get('total_cost_usd', 0.0),
                        model_type=token_summary.get('model_type', 'gemini-pro')
                    )
            except Exception as e:
                logger.warn(f"Failed to get token usage: {e}")

        end_time = datetime.now().isoformat()

        logger.info("Document processing completed successfully")
        logger.info(f"Generated {len(output_files)} output files")

        if token_usage:
            logger.info(f"Token usage: {token_usage.total_tokens} tokens, ${token_usage.cost_usd:.4f}")
        
        return PipelineStepResult(
            step_id=step_id,
            status=NodeStatus.COMPLETED,
            start_time=start_time,
            end_time=end_time,
            output_files=output_files,
            token_usage=token_usage,
            metadata={
                "processing_parameters": parameters.model_dump(),
                "input_file": file_path,
                "output_directory": output_dir
            }
        )
        
    except Exception as e:
        end_time = datetime.now().isoformat()

        logger.error(f"Document processing failed: {str(e)}")

        return PipelineStepResult(
            step_id=step_id,
            status=NodeStatus.ERROR,
            start_time=start_time,
            end_time=end_time,
            output_files=[],
            error_message=str(e),
            metadata={
                "processing_parameters": parameters.model_dump(),
                "input_file": file_path,
                "output_directory": output_dir
            }
        )