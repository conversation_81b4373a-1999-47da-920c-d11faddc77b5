/**
 * App Component - UI Only
 * Static layout display without logic
 */
import React from "react";
import { Header } from "./components/Header";
import { PipelineNavigator } from "./components/PipelineNavigator";
import { Inspector } from "./components/Inspector";
import { LiveTerminal } from "./components/LiveTerminal";

function App() {
    return (
        <div className="h-screen bg-gray-900 flex flex-col">
            {/* Header */}
            <Header />
            
            {/* Main Content */}
            <div className="flex-1 flex overflow-hidden">
                {/* Left Sidebar - Pipeline Navigator */}
                <PipelineNavigator />
                
                {/* Center Content - Inspector */}
                <div className="flex-1 flex flex-col">
                    <Inspector />
                </div>
            </div>
            
            {/* Bottom Terminal */}
            <div className="border-t border-gray-700">
                <LiveTerminal height={200} />
            </div>
        </div>
    );
}

export default App;
