/**
 * Pipeline Navigator Component - UI Only
 * Static pipeline display without logic
 */
import React from "react";
import {
    Upload,
    FileText,
    Play,
    CheckCircle,
    Clock,
    Wifi,
} from "lucide-react";

// Static pipeline configuration
const PIPELINE_STEPS = [
    { id: "document_processor", name: "Document Processor", number: 1 },
    { id: "business_flow_detector", name: "Business Flow Detector", number: 2 },
    { id: "extract_and_process_to_json", name: "Extract and Process to JSON", number: 3 },
    { id: "gen_html_structure_uidl", name: "Generate HTML Structure UIDL", number: 4 },
    { id: "gen_test_case", name: "Generate Test Case", number: 5 },
    { id: "test_case_evaluator", name: "Test Case Evaluator", number: 6 },
];

export const PipelineNavigator: React.FC = () => {
    return (
        <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-700">
                <div className="flex items-center gap-2 mb-3">
                    <Wifi className="w-5 h-5 text-green-500" />
                    <h2 className="text-xl font-bold text-white">Pipeline Navigator</h2>
                </div>
                <p className="text-gray-400 text-sm">
                    Upload a document and execute pipeline steps
                </p>
            </div>

            {/* File Upload Section */}
            <div className="p-4 border-b border-gray-700">
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                        Upload Document
                    </label>
                    <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-gray-500 transition-colors">
                        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-400 mb-1">
                            Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">
                            PDF, DOC, DOCX, TXT files
                        </p>
                    </div>
                </div>

                {/* Current File Display */}
                <div className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-blue-400" />
                        <span className="text-sm text-gray-300">sample-document.pdf</span>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                        Uploaded • 2.3 MB
                    </div>
                </div>
            </div>

            {/* Pipeline Steps */}
            <div className="flex-1 overflow-y-auto">
                <div className="p-4">
                    <h3 className="text-sm font-medium text-gray-300 mb-3">
                        Pipeline Steps
                    </h3>
                    <div className="space-y-2">
                        {PIPELINE_STEPS.map((step, index) => (
                            <div
                                key={step.id}
                                className="bg-gray-700 rounded-lg p-3 border border-gray-600"
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-xs text-white font-medium">
                                            {step.number}
                                        </div>
                                        <div>
                                            <div className="text-sm font-medium text-white">
                                                {step.name}
                                            </div>
                                            <div className="text-xs text-gray-400">
                                                {index === 0 ? "Completed" : 
                                                 index === 1 ? "Running" : "Pending"}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {index === 0 ? (
                                            <CheckCircle className="w-4 h-4 text-green-500" />
                                        ) : index === 1 ? (
                                            <Clock className="w-4 h-4 text-yellow-500 animate-spin" />
                                        ) : (
                                            <Clock className="w-4 h-4 text-gray-500" />
                                        )}
                                        <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors">
                                            <Play className="w-3 h-3" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Execute All Button */}
            <div className="p-4 border-t border-gray-700">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
                    <Play className="w-4 h-4" />
                    Execute All Steps
                </button>
            </div>
        </div>
    );
};
