/**
 * LiveTerminal Component - UI Only
 * Static terminal display without logic
 */
import React from "react";
import { Search, Copy, Download, Maximize2, Wifi } from "lucide-react";

interface LiveTerminalProps {
    height?: number;
    onResize?: (height: number) => void;
    className?: string;
}

export const LiveTerminal: React.FC<LiveTerminalProps> = ({
    height = 300,
    className = "",
}) => {
    return (
        <div
            className={`bg-gray-900 border border-gray-700 rounded-lg ${className}`}
        >
            {/* Terminal Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-700 bg-gray-800 rounded-t-lg">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                        <Wifi className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-300">Terminal</span>
                    </div>
                    <div className="text-xs text-gray-500">
                        Connected • 0 lines
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <button className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <Search className="w-4 h-4" />
                    </button>
                    <button className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <Copy className="w-4 h-4" />
                    </button>
                    <button className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <Download className="w-4 h-4" />
                    </button>
                    <button className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <Maximize2 className="w-4 h-4" />
                    </button>
                </div>
            </div>

            {/* Terminal Content */}
            <div
                className="bg-black text-green-400 font-mono text-sm p-4 overflow-auto"
                style={{ height: `${height}px` }}
            >
                <div className="whitespace-pre-wrap">
                    <span className="text-gray-500">$ </span>
                    <span>Welcome to Phoenix Pipeline Terminal</span>
                    {"\n"}
                    <span className="text-gray-500">$ </span>
                    <span>Waiting for pipeline execution...</span>
                    {"\n"}
                    <span className="text-gray-500">$ </span>
                    <span className="animate-pulse">_</span>
                </div>
            </div>
        </div>
    );
};
